<?php

namespace App\Http\Controllers\DashboardAPI;

use App\Http\Requests\Dashboard\GetCountyZipCodesByZipCodeRequest;
use App\Enums\Timezone;
use App\Http\Resources\Dashboard\LocalityData\LocalityCountyResource;
use App\Http\Resources\Dashboard\LocalityData\LocalityStateResource;
use App\Http\Resources\Dashboard\LocalityData\LocalityZipCodeResource;
use App\Models\Legacy\Location;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Repositories\Legacy\DatabaseLocationRepository;
use App\Repositories\LocationRepository;
use App\Services\Dashboard\DashboardAuthService;
use App\Services\Dashboard\JWT\DashboardJWTService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;

class ReferenceDataController extends BaseDashboardApiController
{
    const RESPONSE_STATUS       = 'status';
    const RESPONSE_COUNTRIES    = 'countries';
    const RESPONSE_STATES       = 'states';
    const RESPONSE_COUNTIES     = 'counties';
    const RESPONSE_ZIP_CODES    = 'zip_codes';
    const RESPONSE_MESSAGE      = 'message';
    const RESPONSE_SERVICES     = 'services';
    const RESPONSE_TIMEZONES    = 'timezones';
    const RESPONSE_DST          = 'dst';

    public function __construct(
        Request              $request,
        DashboardAuthService $authService,
        DashboardJWTService  $jwtService,
        protected LocationRepository            $locationRepository,
        protected DatabaseLocationRepository    $databaseLocationRepository,
    )
    {
        parent::__construct($request, $authService, $jwtService);
    }

    /**
     * Implement when required
     * @return JsonResponse
     */
    public function getCountries(): JsonResponse
    {
        return $this->formatResponse([
            self::RESPONSE_STATUS       => true,
            self::RESPONSE_COUNTRIES    => $this->locationRepository->getCountries()->toArray(),
        ]);
    }

    /**
     * List of states with zip totals
     * @return JsonResponse
     */
    public function getStatesWithTotals(): JsonResponse
    {
        $states = $this->getStatesWithTotalsQuery()
            ->get();

        return $this->formatResponse([
            self::RESPONSE_STATUS   => true,
            self::RESPONSE_STATES   => LocalityStateResource::collection($states),
            self::RESPONSE_COUNTIES => $this->getCountyListByState(),
        ]);
    }

    public function getStatesAndTimezones(): JsonResponse
    {
        return $this->formatResponse([
            self::RESPONSE_STATUS    => true,
            self::RESPONSE_STATES    => LocalityStateResource::collection($this->getStatesWithTotalsQuery()->get()),
            self::RESPONSE_COUNTIES  => $this->getCountyListByState(),
            self::RESPONSE_TIMEZONES => $this->getTimezoneDisplayData(true),
            self::RESPONSE_DST       => Timezone::isDST(),
        ]);
    }

    private function getCountyListByState(): array
    {
        $output = [];
        $counties = $this->locationRepository->getAllCounties();
        $counties->each(function(Location $county) use (&$output) {
            $output[$county->state_abbr] = $output[$county->state_abbr] ?? [];
            $output[$county->state_abbr][] = [
                'county_key'    => $county->county_key,
                'county'        => $county->county,
                'id'            => $county->id
            ];
        });

        return collect($output)
            ->transform(fn($arr) => collect($arr)->sortBy('county')->values())
            ->toArray();
    }

    /**
     * Get county list and zipcode totals for a state
     * @param string $stateKey
     * @return JsonResponse
     */
    public function getStateDetail(string $stateKey): JsonResponse
    {
        $counties = $this->locationRepository->getCountiesInStateByAbbreviation($stateKey);
        $zipTotals = $this->locationRepository->totalZipCodesPerCounty($stateKey);

        $counties->each(fn($county) => $county['total_zip_codes'] = $zipTotals[$county['county_key']] ?? null)
            ->sortBy('county', SORT_REGULAR, false);

        return $this->formatResponse([
            self::RESPONSE_STATUS   => true,
            self::RESPONSE_COUNTIES => LocalityCountyResource::collection($counties),
        ]);
    }

    /**
     * Get zipcode detail for a county
     * @param string $countyKey
     * @return JsonResponse
     */
    public function getCountyDetail(string $stateKey, string $countyKey): JsonResponse
    {
        $zipCodes = $this->locationRepository->getZipCodesByCounty($stateKey, $countyKey);

        return $this->formatResponse([
            self::RESPONSE_STATUS    => true,
            self::RESPONSE_ZIP_CODES => LocalityZipCodeResource::collection($zipCodes),
        ]);
    }

    /**
     * Get County detail & zipcodes for every county in a state
     * @param string $stateKey
     * @return JsonResponse
     */
    public function getAllZipCodesInState(string $stateKey): JsonResponse
    {
        if ($stateKey) {
            $zipCodes = LocalityZipCodeResource::collection($this->locationRepository->getAllZipCodesInState($stateKey));
            $zipCodesGrouped = collect($zipCodes)->groupBy(Location::COUNTY_KEY);
        }
        return $this->formatResponse([
            self::RESPONSE_STATUS    => !!($zipCodesGrouped ?? false),
            self::RESPONSE_ZIP_CODES => $zipCodesGrouped ?? [],
        ]);
    }

    /**
     * @param string $zipCode
     * @param int $radius
     * @return JsonResponse
     */
    public function getZipCodesByRadius(string $zipCode, int $radius): JsonResponse
    {
        $zipCodes = $this->locationRepository->getZipCodesByRadius($zipCode, $radius);

        return $this->formatResponse([
            self::RESPONSE_STATUS       => true,
            self::RESPONSE_ZIP_CODES    => LocalityZipCodeResource::collection($zipCodes)
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getZipCodesByZipCodeStrings(): JsonResponse
    {
        $zipCodeArray = $this->request->get(self::RESPONSE_ZIP_CODES);
        if (!$zipCodeArray || gettype($zipCodeArray) !== 'array') {
            return $this->formatResponse([
                self::RESPONSE_STATUS   => false,
                self::RESPONSE_MESSAGE  => "Zip codes must be provided as an array."
            ]);
        }
        else {
            $zipCodes =  $this->locationRepository->getZipCodesByZipCodeStrings($zipCodeArray);
            return $this->formatResponse([
                self::RESPONSE_STATUS    => true,
                self::RESPONSE_ZIP_CODES => LocalityZipCodeResource::collection($zipCodes),
            ]);
        }
    }

    /**
     * @return JsonResponse
     */
    public function getServicesByIndustry(): JsonResponse
    {
        $groupByIndustry = function (Collection $services): Collection {
            return $services
                ->groupBy(IndustryService::RELATION_INDUSTRY . '.' . Industry::FIELD_NAME)
                ->map(fn($industryGroup) => $industryGroup->map(fn(IndustryService $service) => [
                    'name'  => $service->{IndustryService::FIELD_NAME},
                    'slug'  => $service->{IndustryService::FIELD_SLUG},
                    'id'    => $service->{IndustryService::FIELD_ID},
                    'industry_id'   => $service->{IndustryService::RELATION_INDUSTRY}?->{Industry::FIELD_ID},
                    'industry'      => $service->{IndustryService::RELATION_INDUSTRY}?->{Industry::FIELD_NAME}
                ]
                ));
        };
        return $this->formatResponse([
            self::RESPONSE_STATUS   => true,
            self::RESPONSE_SERVICES => $groupByIndustry(IndustryService::all())
        ]);
    }

    /**
     * Return all zip codes from counties
     * @param GetCountyZipCodesByZipCodeRequest $request
     * @return JsonResponse
     */
    public function getCountyZipCodesByZipCodes(GetCountyZipCodesByZipCodeRequest $request): JsonResponse
    {
        $zipCodes = $this->locationRepository->getCountyZipCodesByZipCodes($request[GetCountyZipCodesByZipCodeRequest::FIELD_ZIP_CODES]);

        return $this->formatResponse([
            self::RESPONSE_STATUS   => true,
            self::RESPONSE_ZIP_CODES => $zipCodes
        ]);
    }

    public function getTimezones(): JsonResponse
    {
        return $this->formatResponse([
            "status" => true,
            "timezones" => $this->getTimezoneDisplayData(),
        ]);
    }

    /**
     * @param bool $checkDST
     * @return array
     */
    private function getTimezoneDisplayData(bool $checkDST = false): array
    {
        $dst = $checkDST && Timezone::isDST();

        return array_flip(Timezone::displayNames($dst));
    }

    /**
     * @return Builder
     */
    private function getStatesWithTotalsQuery(): Builder
    {
        $locations2 = 'locations2';
        $locations3 = 'locations3';

        return Location::query()
            ->selectRaw(Location::TABLE . ".*, count(distinct $locations2.id) as " . LocalityStateResource::KEY_TOTAL_COUNTIES . ", count(distinct $locations3.id) as " . LocalityStateResource::KEY_TOTAL_ZIP_CODES)
            ->join(Location::TABLE . " as $locations2", fn(JoinClause $join) =>
            $join->on(Location::TABLE .'.'. Location::STATE_ABBREVIATION, '=', $locations2 .'.'. Location::STATE_ABBREVIATION)
                ->where($locations2 .'.'. Location::TYPE, '=', Location::TYPE_COUNTY)
            )->join(Location::TABLE . " as $locations3", fn(JoinClause $join) =>
            $join->on(Location::TABLE .'.'. Location::STATE_ABBREVIATION, '=', $locations3 .'.'. Location::STATE_ABBREVIATION)
                ->where($locations3 .'.'. Location::TYPE, '=', Location::TYPE_ZIP_CODE)
            )->where(Location::TABLE .'.'. Location::TYPE, Location::TYPE_STATE)
            ->groupBy(Location::TABLE .'.'. Location::STATE_ABBREVIATION);
    }
}
